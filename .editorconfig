# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = crlf
insert_final_newline = true
trim_trailing_whitespace = true

# Code files
[*.{cs,csx,vb,vbx}]
indent_style = space
indent_size = 4

# XML project files
[*.{csproj,vbproj,vcxproj,vcxproj.filters,proj,projitems,shproj}]
indent_style = space
indent_size = 2

# XML config files
[*.{props,targets,ruleset,config,nuspec,resx,vsixmanifest,vsct}]
indent_style = space
indent_size = 2

# JSON files
[*.{json,json5}]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false

# C# files
[*.cs]

# New line preferences
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
csharp_new_line_before_members_in_object_initializers = true
csharp_new_line_before_members_in_anonymous_types = true
csharp_new_line_between_query_expression_clauses = true

# Indentation preferences
csharp_indent_case_contents = true
csharp_indent_switch_labels = true
csharp_indent_labels = flush_left

# Space preferences
csharp_space_after_cast = false
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_parentheses = false
csharp_space_before_colon_in_inheritance_clause = true
csharp_space_after_colon_in_inheritance_clause = true
csharp_space_around_binary_operators = before_and_after
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false
csharp_space_between_method_call_name_and_opening_parenthesis = false
csharp_space_between_method_call_empty_parameter_list_parentheses = false

# Wrapping preferences
csharp_preserve_single_line_statements = true
csharp_preserve_single_line_blocks = true

# Code style rules
dotnet_style_qualification_for_field = false:suggestion
dotnet_style_qualification_for_property = false:suggestion
dotnet_style_qualification_for_method = false:suggestion
dotnet_style_qualification_for_event = false:suggestion
dotnet_style_predefined_type_for_locals_parameters_members = true:suggestion
dotnet_style_predefined_type_for_member_access = true:suggestion
dotnet_style_require_accessibility_modifiers = for_non_interface_members:suggestion
dotnet_style_readonly_field = true:suggestion

# Expression-level preferences
dotnet_style_object_initializer = true:suggestion
dotnet_style_collection_initializer = true:suggestion
dotnet_style_explicit_tuple_names = true:suggestion
dotnet_style_null_propagation = true:suggestion
dotnet_style_coalesce_expression = true:suggestion
dotnet_style_prefer_is_null_check_over_reference_equality_method = true:suggestion
dotnet_style_prefer_inferred_tuple_names = true:suggestion
dotnet_style_prefer_inferred_anonymous_type_member_names = true:suggestion
dotnet_style_prefer_auto_properties = true:silent
dotnet_style_prefer_conditional_expression_over_assignment = true:silent
dotnet_style_prefer_conditional_expression_over_return = true:silent

# C# expression-level preferences
csharp_prefer_simple_default_expression = true:suggestion
csharp_style_pattern_matching_over_is_with_cast_check = true:suggestion
csharp_style_pattern_matching_over_as_with_null_check = true:suggestion
csharp_style_inlined_variable_declaration = true:suggestion
csharp_prefer_simple_using_statement = true:suggestion
csharp_style_deconstructed_variable_declaration = true:suggestion
csharp_style_pattern_local_over_anonymous_function = true:suggestion
csharp_using_directive_placement = outside_namespace:silent
csharp_prefer_static_local_functions = true:suggestion
csharp_prefer_simple_using_statement = true:suggestion

# C# expression-bodied members
csharp_style_expression_bodied_methods = false:silent
csharp_style_expression_bodied_constructors = false:silent
csharp_style_expression_bodied_operators = false:silent
csharp_style_expression_bodied_properties = true:silent
csharp_style_expression_bodied_indexers = true:silent
csharp_style_expression_bodied_accessors = true:silent

# Naming conventions
dotnet_naming_rule.interface_should_be_prefixed_with_i.severity = suggestion
dotnet_naming_rule.interface_should_be_prefixed_with_i.symbols = interface
dotnet_naming_rule.interface_should_be_prefixed_with_i.style = prefixed_with_i

dotnet_naming_rule.types_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.types_should_be_pascal_case.symbols = types
dotnet_naming_rule.types_should_be_pascal_case.style = pascal_case

dotnet_naming_rule.non_field_members_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.non_field_members_should_be_pascal_case.symbols = non_field_members
dotnet_naming_rule.non_field_members_should_be_pascal_case.style = pascal_case

# Symbol specifications
dotnet_naming_symbols.interface.applicable_kinds = interface
dotnet_naming_symbols.interface.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.interface.required_modifiers = 

dotnet_naming_symbols.types.applicable_kinds = class, struct, interface, enum
dotnet_naming_symbols.types.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.types.required_modifiers = 

dotnet_naming_symbols.non_field_members.applicable_kinds = property, event, method
dotnet_naming_symbols.non_field_members.applicable_accessibilities = public, internal, private, protected, protected_internal, private_protected
dotnet_naming_symbols.non_field_members.required_modifiers = 

# Naming styles
dotnet_naming_style.pascal_case.required_prefix = 
dotnet_naming_style.pascal_case.required_suffix = 
dotnet_naming_style.pascal_case.word_separator = 
dotnet_naming_style.pascal_case.capitalization = pascal_case

dotnet_naming_style.prefixed_with_i.required_prefix = I
dotnet_naming_style.prefixed_with_i.required_suffix = 
dotnet_naming_style.prefixed_with_i.word_separator = 
dotnet_naming_style.prefixed_with_i.capitalization = pascal_case

# Severity levels for code analysis
dotnet_diagnostic.CS8600.severity = warning  # Converting null literal or possible null value to non-nullable type
dotnet_diagnostic.CS8601.severity = warning  # Possible null reference assignment
dotnet_diagnostic.CS8602.severity = warning  # Dereference of a possibly null reference
dotnet_diagnostic.CS8603.severity = warning  # Possible null reference return
dotnet_diagnostic.CS8604.severity = warning  # Possible null reference argument
dotnet_diagnostic.CS8618.severity = warning  # Non-nullable field must contain a non-null value when exiting constructor
dotnet_diagnostic.CS8625.severity = warning  # Cannot convert null literal to non-nullable reference type

# Additional code quality rules
dotnet_diagnostic.CA1031.severity = suggestion  # Do not catch general exception types
dotnet_diagnostic.CA1062.severity = suggestion  # Validate arguments of public methods
dotnet_diagnostic.CA1303.severity = none       # Do not pass literals as localized parameters
dotnet_diagnostic.CA1304.severity = suggestion  # Specify CultureInfo
dotnet_diagnostic.CA1305.severity = suggestion  # Specify IFormatProvider
dotnet_diagnostic.CA1307.severity = suggestion  # Specify StringComparison
dotnet_diagnostic.CA1308.severity = suggestion  # Normalize strings to uppercase
dotnet_diagnostic.CA1309.severity = suggestion  # Use ordinal StringComparison
dotnet_diagnostic.CA1310.severity = suggestion  # Specify StringComparison for correctness
dotnet_diagnostic.CA1507.severity = suggestion  # Use nameof to express symbol names
dotnet_diagnostic.CA1508.severity = suggestion  # Avoid dead conditional code
dotnet_diagnostic.CA1509.severity = error      # Invalid entry in code metrics rule specification file
dotnet_diagnostic.CA1802.severity = suggestion  # Use literals where appropriate
dotnet_diagnostic.CA1805.severity = suggestion  # Do not initialize unnecessarily
dotnet_diagnostic.CA1812.severity = suggestion  # Avoid uninstantiated internal classes
dotnet_diagnostic.CA1813.severity = suggestion  # Avoid unsealed attributes
dotnet_diagnostic.CA1814.severity = suggestion  # Prefer jagged arrays over multidimensional
dotnet_diagnostic.CA1815.severity = suggestion  # Override equals and operator equals on value types
dotnet_diagnostic.CA1816.severity = suggestion  # Dispose methods should call SuppressFinalize
dotnet_diagnostic.CA1819.severity = suggestion  # Properties should not return arrays
dotnet_diagnostic.CA1820.severity = suggestion  # Test for empty strings using string length
dotnet_diagnostic.CA1821.severity = suggestion  # Remove empty Finalizers
dotnet_diagnostic.CA1822.severity = suggestion  # Mark members as static
dotnet_diagnostic.CA1823.severity = suggestion  # Avoid unused private fields
dotnet_diagnostic.CA1824.severity = suggestion  # Mark assemblies with NeutralResourcesLanguageAttribute
dotnet_diagnostic.CA1825.severity = suggestion  # Avoid zero-length array allocations
dotnet_diagnostic.CA1826.severity = suggestion  # Do not use Enumerable methods on indexable collections
dotnet_diagnostic.CA1827.severity = suggestion  # Do not use Count() or LongCount() when Any() can be used
dotnet_diagnostic.CA1828.severity = suggestion  # Do not use CountAsync() or LongCountAsync() when AnyAsync() can be used
dotnet_diagnostic.CA1829.severity = suggestion  # Use Length/Count property instead of Count() when available
dotnet_diagnostic.CA1830.severity = suggestion  # Prefer strongly-typed Append and Insert method overloads on StringBuilder
dotnet_diagnostic.CA1831.severity = suggestion  # Use AsSpan or AsMemory instead of Range-based indexers when appropriate
dotnet_diagnostic.CA1832.severity = suggestion  # Use AsSpan or AsMemory instead of Range-based indexers when appropriate
dotnet_diagnostic.CA1833.severity = suggestion  # Use AsSpan or AsMemory instead of Range-based indexers when appropriate
dotnet_diagnostic.CA1834.severity = suggestion  # Consider using 'StringBuilder.Append(char)' when applicable
dotnet_diagnostic.CA1835.severity = suggestion  # Prefer the 'Memory'-based overloads for 'ReadAsync' and 'WriteAsync'
dotnet_diagnostic.CA1836.severity = suggestion  # Prefer IsEmpty over Count
dotnet_diagnostic.CA1837.severity = suggestion  # Use 'Environment.ProcessId'
dotnet_diagnostic.CA1838.severity = suggestion  # Avoid 'StringBuilder' parameters for P/Invokes
dotnet_diagnostic.CA1839.severity = suggestion  # Use 'Environment.ProcessPath'
dotnet_diagnostic.CA1840.severity = suggestion  # Use 'Environment.CurrentManagedThreadId'
dotnet_diagnostic.CA1841.severity = suggestion  # Prefer Dictionary.Contains methods
dotnet_diagnostic.CA1842.severity = suggestion  # Do not use 'WhenAll' with a single task
dotnet_diagnostic.CA1843.severity = suggestion  # Do not use 'WaitAll' with a single task
dotnet_diagnostic.CA1844.severity = suggestion  # Provide memory-based overrides of async methods when subclassing 'Stream'
dotnet_diagnostic.CA1845.severity = suggestion  # Use span-based 'string.Concat'
dotnet_diagnostic.CA1846.severity = suggestion  # Prefer 'AsSpan' over 'Substring'
dotnet_diagnostic.CA1847.severity = suggestion  # Use char literal for a single character lookup
dotnet_diagnostic.CA1848.severity = suggestion  # Use the LoggerMessage delegates
dotnet_diagnostic.CA1849.severity = suggestion  # Call async methods when in an async method
dotnet_diagnostic.CA2007.severity = suggestion  # Consider calling ConfigureAwait on the awaited task
dotnet_diagnostic.CA2008.severity = suggestion  # Do not create tasks without passing a TaskScheduler
dotnet_diagnostic.CA2009.severity = suggestion  # Do not call ToImmutableCollection on an ImmutableCollection value
dotnet_diagnostic.CA2011.severity = suggestion  # Avoid infinite recursion
dotnet_diagnostic.CA2012.severity = suggestion  # Use ValueTasks correctly
dotnet_diagnostic.CA2013.severity = suggestion  # Do not use ReferenceEquals with value types
dotnet_diagnostic.CA2014.severity = suggestion  # Do not use stackalloc in loops
dotnet_diagnostic.CA2015.severity = suggestion  # Do not define finalizers for types derived from MemoryManager<T>
dotnet_diagnostic.CA2016.severity = suggestion  # Forward the 'CancellationToken' parameter to methods that take one
