﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <PackageId>unsplasharp.api</PackageId>
    <Version>0.8.0</Version>
    <Authors><PERSON><PERSON><PERSON> CORPINOT</Authors>
    <Description>Unsplash API for .NET - Modernized for .NET Standard 2.0</Description>
    <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
    <PackageReleaseNotes>
      Updated to .NET Standard 2.0, modernized dependencies, improved HttpClient usage patterns.
    </PackageReleaseNotes>
    <Copyright>MIT Licence. All rights reserved.</Copyright>
    <PackageTags>unsplash api photography images</PackageTags>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <PackageProjectUrl>https://github.com/rootasjey/unsplasharp</PackageProjectUrl>
    <RepositoryUrl>https://github.com/rootasjey/unsplasharp</RepositoryUrl>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors>CS8600;CS8601;CS8602;CS8603;CS8604;CS8618;CS8625</WarningsNotAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <DocumentationFile>bin\Release\netstandard2.0\unsplasharp.xml</DocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="Polly" Version="8.2.0" />
  </ItemGroup>

</Project>